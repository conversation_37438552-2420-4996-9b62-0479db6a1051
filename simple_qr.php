<?php
/**
 * Simple QR Code Generator using Google Charts API
 * This is a fallback solution for QR code generation
 */

class SimpleQR {
    
    /**
     * Generate QR code using Google Charts API
     * @param string $data The data to encode
     * @param string $filename The filename to save to (optional)
     * @param int $size The size of the QR code (default 200)
     * @return bool Success status
     */
    public static function png($data, $filename = false, $size = 200) {
        // URL encode the data
        $encoded_data = urlencode($data);
        
        // Google Charts QR Code API URL
        $url = "https://chart.googleapis.com/chart?chs={$size}x{$size}&cht=qr&chl={$encoded_data}";
        
        // Get the QR code image
        $qr_image = file_get_contents($url);
        
        if ($qr_image === false) {
            return false;
        }
        
        if ($filename !== false) {
            // Save to file
            $result = file_put_contents($filename, $qr_image);
            return $result !== false;
        } else {
            // Output directly to browser
            header('Content-Type: image/png');
            echo $qr_image;
            return true;
        }
    }
    
    /**
     * Generate QR code and return as base64 data URL
     * @param string $data The data to encode
     * @param int $size The size of the QR code (default 200)
     * @return string Base64 data URL
     */
    public static function getDataURL($data, $size = 200) {
        $encoded_data = urlencode($data);
        $url = "https://chart.googleapis.com/chart?chs={$size}x{$size}&cht=qr&chl={$encoded_data}";
        
        $qr_image = file_get_contents($url);
        
        if ($qr_image === false) {
            return false;
        }
        
        $base64 = base64_encode($qr_image);
        return "data:image/png;base64," . $base64;
    }
    
    /**
     * Generate QR code HTML img tag
     * @param string $data The data to encode
     * @param int $size The size of the QR code (default 200)
     * @param string $alt Alt text for the image
     * @return string HTML img tag
     */
    public static function getImageTag($data, $size = 200, $alt = "QR Code") {
        $dataURL = self::getDataURL($data, $size);
        if ($dataURL === false) {
            return '<p>Error generating QR code</p>';
        }
        
        return '<img src="' . $dataURL . '" alt="' . htmlspecialchars($alt) . '" width="' . $size . '" height="' . $size . '">';
    }
}

// For compatibility with existing code, create aliases
if (!class_exists('QRcode')) {
    class QRcode {
        public static function png($text, $outfile = false, $level = 'L', $size = 3, $margin = 4, $saveandprint = false) {
            // Convert size parameter (phpqrcode uses different scale)
            $pixel_size = $size * 25; // Approximate conversion
            
            return SimpleQR::png($text, $outfile, $pixel_size);
        }
        
        public static function text($text, $outfile = false, $level = 'L', $size = 3, $margin = 4) {
            // This method is not supported in the simple implementation
            // Return a simple text representation
            return array("Simple QR implementation - text mode not supported");
        }
    }
}

// Define constants for compatibility
if (!defined('QR_ECLEVEL_L')) {
    define('QR_ECLEVEL_L', 'L');
    define('QR_ECLEVEL_M', 'M');
    define('QR_ECLEVEL_Q', 'Q');
    define('QR_ECLEVEL_H', 'H');
}
?>
