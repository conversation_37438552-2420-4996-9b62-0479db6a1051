 
<style type="text/css">
  /* Modern Sidebar Styles */
  :root {
    --primary-color: #175883;
    --primary-dark: #0d3c60;
    --primary-light: #2980b9;
    --accent-color: #cbb09c;
    --text-light: #ffffff;
    --text-dark: #333333;
    --bg-light: #f8f9fa;
    --bg-dark: #2d3a4b;
    --success-color: #2ecc71;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --info-color: #3498db;
  }

  #menuToggle {
    display: none;
  }

  #menuIcon {
    cursor: pointer;
    display: block;
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 1001;
    font-size: 24px;
    background: var(--primary-color);
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
  }

  #menuIcon:hover {
    background-color: var(--primary-dark);
  }

  #menu {
    position: fixed;
    top: 0;
    left: -280px;
    width: 280px;
    height: 100%;
    background: var(--bg-dark);
    transition: all 0.3s ease-in-out;
    z-index: 1000;
    padding-top: 70px;
    box-shadow: none;
    overflow-y: auto;
  }

  #menuToggle:checked ~ #menu {
    left: 0;
    box-shadow: 2px 0 15px rgba(0,0,0,0.1);
  }

  #menu .sidebar-header {
    padding: 20px;
    text-align: center;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    margin-bottom: 15px;
  }

  #menu .sidebar-header h3 {
    color: var(--text-light);
    margin: 0;
    font-size: 1.5rem;
  }

  #menu .sidebar-user {
    padding: 0 20px 20px;
    text-align: center;
    color: var(--text-light);
    border-bottom: 1px solid rgba(255,255,255,0.1);
    margin-bottom: 15px;
  }

  #menu .sidebar-user .user-image {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    margin: 0 auto 10px;
    background: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
  }

  #menu .sidebar-user .user-name {
    font-weight: 500;
    margin-bottom: 5px;
  }

  #menu .sidebar-user .user-role {
    font-size: 12px;
    opacity: 0.7;
  }

  #menu .menu-section {
    margin-bottom: 15px;
  }

  #menu .menu-section-title {
    padding: 10px 20px;
    font-size: 12px;
    text-transform: uppercase;
    color: rgba(255,255,255,0.5);
    letter-spacing: 1px;
  }

  #menu a {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    text-decoration: none;
    color: var(--text-light);
    transition: all 0.3s;
    border-left: 4px solid transparent;
    font-size: 14px;
  }

  #menu a i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
    font-size: 16px;
  }

  #menu a:hover, #menu a.active {
    background: rgba(255,255,255,0.1);
    border-left: 4px solid var(--accent-color);
    color: var(--accent-color);
  }

  #menu .logout-link {
    position: absolute;
    bottom: 20px;
    width: 100%;
  }

  #menu .logout-link a {
    background-color: rgba(231, 76, 60, 0.2);
    color: #e74c3c;
    margin: 0 20px;
    border-radius: 4px;
    text-align: center;
  }

  #menu .logout-link a:hover {
    background-color: #e74c3c;
    color: white;
    border-left: 4px solid #e74c3c;
  }

  /* Content area adjustment */
  .main-content {
    margin-left: 0;
    padding: 20px;
    transition: margin-left 0.3s ease;
  }

  #menuToggle:checked ~ .main-content {
    margin-left: 280px;
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    #menuToggle:checked ~ .main-content {
      margin-left: 0;
      opacity: 0.4;
    }
  }
</style>

<!-- Link to Font Awesome for icons -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">

<!-- Collapsible Sidebar -->
<input type="checkbox" id="menuToggle">
<label for="menuToggle" id="menuIcon"><i class="fas fa-bars"></i></label>

<div id="menu">
  <div class="sidebar-header">
    <h3>Admin Portal</h3>
  </div>
  
  <div class="sidebar-user">
    <div class="user-image">
      <i class="fas fa-user-shield"></i>
    </div>
    <div class="user-name"><?php echo isset($_SESSION['name']) ? htmlspecialchars($_SESSION['name']) : 'Administrator'; ?></div>
    <div class="user-role">System Administrator</div>
  </div>
  
  <div class="menu-section">
    <div class="menu-section-title">Main Navigation</div>
    <a href="index.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
    <a href="users.php"><i class="fas fa-users"></i> Manage Users</a>
    <a href="venues.php"><i class="fas fa-building"></i> Manage Venues</a>
    <a href="timetables.php"><i class="fas fa-calendar-alt"></i> Timetables</a>
  </div>
  
  <div class="menu-section">
    <div class="menu-section-title">Quick Actions</div>
    <a href="upload_timetable.php"><i class="fas fa-file-upload"></i> Upload Timetable</a>
  </div>
  
  <div class="menu-section">
    <div class="menu-section-title">Reports & Requests</div>
    <a href="view_reports.php"><i class="fas fa-clipboard-list"></i> View Reports</a>
    <a href="requests.php"><i class="fas fa-key"></i> Password Requests</a>
    <a href="notifications.php"><i class="fas fa-bell"></i> Notifications</a>
  </div>
  
  <div class="menu-section">
    <div class="menu-section-title">Settings</div>
    <a href="system_settings.php"><i class="fas fa-cogs"></i> System Settings</a>
    <a href="change_password.php"><i class="fas fa-lock"></i> Change Password</a>
  </div>
  
  <div class="logout-link">
    <a href="logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a>
  </div>
</div>

<div class="main-content">
  <!-- Your page content will go here -->
</div>

<script>
  // Add active class to current page link
  document.addEventListener('DOMContentLoaded', function() {
    const currentPage = window.location.pathname.split('/').pop();
    const menuLinks = document.querySelectorAll('#menu a');
    
    menuLinks.forEach(link => {
      const linkPage = link.getAttribute('href');
      if (linkPage === currentPage) {
        link.classList.add('active');
      }
    });
  });
</script>

  <!-- Materialize CSS Script -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/js/materialize.min.js"></script>
</body>
</html>
