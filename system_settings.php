<?php
session_start();

// Check if the user is logged in and is an admin
if (!isset($_SESSION["user"]) || $_SESSION["role"] !== "admin") {
    // Redirect unauthorized users to login page
    header("Location: login.php");
    exit();
}

// Database connection
require_once "database.php";

// Initialize variables
$success_message = $error_message = "";

// Handle settings update
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    if (isset($_POST['update_settings'])) {
        $system_name = trim($_POST['system_name']);
        $admin_email = trim($_POST['admin_email']);
        $max_venues = intval($_POST['max_venues']);
        $session_timeout = intval($_POST['session_timeout']);
        $enable_notifications = isset($_POST['enable_notifications']) ? 1 : 0;
        $enable_qr_codes = isset($_POST['enable_qr_codes']) ? 1 : 0;
        $maintenance_mode = isset($_POST['maintenance_mode']) ? 1 : 0;
        
        // Validate inputs
        if (empty($system_name)) {
            $error_message = "System name is required.";
        } elseif (!filter_var($admin_email, FILTER_VALIDATE_EMAIL)) {
            $error_message = "Please enter a valid admin email.";
        } elseif ($max_venues < 1) {
            $error_message = "Maximum venues must be at least 1.";
        } elseif ($session_timeout < 5) {
            $error_message = "Session timeout must be at least 5 minutes.";
        } else {
            // Check if settings table exists, if not create it
            $check_table = "SHOW TABLES LIKE 'system_settings'";
            $table_result = mysqli_query($conn, $check_table);
            
            if (mysqli_num_rows($table_result) == 0) {
                $create_table = "CREATE TABLE system_settings (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    setting_name VARCHAR(100) NOT NULL UNIQUE,
                    setting_value TEXT NOT NULL,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                )";
                mysqli_query($conn, $create_table);
            }
            
            // Update or insert settings
            $settings = [
                'system_name' => $system_name,
                'admin_email' => $admin_email,
                'max_venues' => $max_venues,
                'session_timeout' => $session_timeout,
                'enable_notifications' => $enable_notifications,
                'enable_qr_codes' => $enable_qr_codes,
                'maintenance_mode' => $maintenance_mode
            ];
            
            $all_updated = true;
            foreach ($settings as $name => $value) {
                $update_sql = "INSERT INTO system_settings (setting_name, setting_value) 
                              VALUES (?, ?) 
                              ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)";
                $stmt = mysqli_prepare($conn, $update_sql);
                mysqli_stmt_bind_param($stmt, "ss", $name, $value);
                
                if (!mysqli_stmt_execute($stmt)) {
                    $all_updated = false;
                    break;
                }
            }
            
            if ($all_updated) {
                $success_message = "System settings updated successfully!";
            } else {
                $error_message = "Error updating system settings: " . mysqli_error($conn);
            }
        }
    }
    
    // Handle database backup
    if (isset($_POST['backup_database'])) {
        $backup_file = 'backup_' . date('Y-m-d_H-i-s') . '.sql';
        $backup_path = __DIR__ . '/backups/' . $backup_file;
        
        // Create backups directory if it doesn't exist
        if (!is_dir(__DIR__ . '/backups/')) {
            mkdir(__DIR__ . '/backups/', 0755, true);
        }
        
        // Simple backup using mysqldump (if available)
        $command = "mysqldump --host=localhost --user=root --password= venue_management > $backup_path";
        $output = shell_exec($command);
        
        if (file_exists($backup_path) && filesize($backup_path) > 0) {
            $success_message = "Database backup created successfully: $backup_file";
        } else {
            $error_message = "Failed to create database backup. Please check server configuration.";
        }
    }
    
    // Handle cache clear
    if (isset($_POST['clear_cache'])) {
        // Clear any cache files if they exist
        $cache_dir = __DIR__ . '/cache/';
        if (is_dir($cache_dir)) {
            $files = glob($cache_dir . '*');
            foreach ($files as $file) {
                if (is_file($file)) {
                    unlink($file);
                }
            }
        }
        $success_message = "System cache cleared successfully!";
    }
}

// Get current settings
$current_settings = [];
$settings_sql = "SELECT setting_name, setting_value FROM system_settings";
$settings_result = mysqli_query($conn, $settings_sql);

if ($settings_result) {
    while ($row = mysqli_fetch_assoc($settings_result)) {
        $current_settings[$row['setting_name']] = $row['setting_value'];
    }
}

// Default values if settings don't exist
$defaults = [
    'system_name' => 'Venue Management System',
    'admin_email' => '<EMAIL>',
    'max_venues' => 100,
    'session_timeout' => 30,
    'enable_notifications' => 1,
    'enable_qr_codes' => 1,
    'maintenance_mode' => 0
];

foreach ($defaults as $key => $value) {
    if (!isset($current_settings[$key])) {
        $current_settings[$key] = $value;
    }
}

// Get system statistics
$stats_sql = "SELECT 
    (SELECT COUNT(*) FROM users) as total_users,
    (SELECT COUNT(*) FROM venues) as total_venues,
    (SELECT COUNT(*) FROM timetables) as total_timetables,
    (SELECT COUNT(*) FROM notifications) as total_notifications";
$stats_result = mysqli_query($conn, $stats_sql);
$stats = mysqli_fetch_assoc($stats_result);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <?php include('CSS/header.php'); ?>
    <title>System Settings | Admin Dashboard</title>
    <style>
        :root {
            --primary-color: #175883;
            --primary-dark: #0d3c60;
            --primary-light: #2980b9;
            --accent-color: #cbb09c;
            --text-light: #ffffff;
            --text-dark: #333333;
            --bg-light: #f8f9fa;
            --bg-dark: #2d3a4b;
            --success-color: #2ecc71;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --info-color: #3498db;
        }
        
        body {
            background-color: var(--bg-light);
            font-family: 'Roboto', sans-serif;
            margin: 0;
            padding: 0;
            color: var(--text-dark);
        }
        
        .container {
            padding: 30px;
            background-color: #f8f9fa;
            border-radius: 10px;
            box-shadow: 0 0 15px rgba(0,0,0,0.05);
            margin: 20px;
            transition: margin-left 0.3s ease;
        }
        
        #menuToggle:checked ~ .container {
            margin-left: 270px;
        }
        
        .page-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
            padding: 25px;
            border-radius: 8px;
            margin-bottom: 30px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .page-header h1 {
            margin: 0 0 10px 0;
            font-size: 28px;
            font-weight: 500;
        }
        
        .page-header p {
            margin: 0;
            font-size: 16px;
            opacity: 0.9;
        }
        
        .settings-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
        }
        
        .settings-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .card-header {
            background: var(--primary-light);
            color: white;
            padding: 15px 20px;
            font-size: 18px;
            font-weight: 500;
        }
        
        .card-body {
            padding: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: var(--text-dark);
        }
        
        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            transition: all 0.3s;
        }
        
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(23, 88, 131, 0.2);
        }
        
        .form-check {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .form-check input[type="checkbox"] {
            margin-right: 10px;
            transform: scale(1.2);
        }
        
        .form-check label {
            margin-bottom: 0;
            cursor: pointer;
        }
        
        .btn {
            padding: 12px 25px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s;
            border: none;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        .btn-primary {
            background: var(--primary-color);
            color: white;
        }
        
        .btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .btn-warning {
            background: var(--warning-color);
            color: white;
        }
        
        .btn-warning:hover {
            background: #e67e22;
        }
        
        .btn-danger {
            background: var(--danger-color);
            color: white;
        }
        
        .btn-danger:hover {
            background: #c0392b;
        }
        
        .alert {
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background-color: rgba(46, 204, 113, 0.2);
            border: 1px solid var(--success-color);
            color: #27ae60;
        }
        
        .alert-danger {
            background-color: rgba(231, 76, 60, 0.2);
            border: 1px solid var(--danger-color);
            color: #c0392b;
        }
        
        .stats-list {
            list-style: none;
            padding: 0;
        }
        
        .stats-list li {
            display: flex;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        
        .stats-list li:last-child {
            border-bottom: none;
        }
        
        .stats-value {
            font-weight: bold;
            color: var(--primary-color);
        }
        
        .maintenance-warning {
            background-color: rgba(243, 156, 18, 0.2);
            border: 1px solid var(--warning-color);
            color: #e67e22;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 15px;
                margin: 10px;
            }
            
            .settings-grid {
                grid-template-columns: 1fr;
            }
            
            .page-header {
                padding: 20px;
            }
            
            .page-header h1 {
                font-size: 24px;
            }
            
            #menuToggle:checked ~ .container {
                margin-left: 20px;
                opacity: 0.4;
            }
        }
    </style>
</head>
<body>

<?php include('CSS/sidebar.php'); ?>

<div class="container">
    <div class="page-header">
        <h1>System Settings</h1>
        <p>Configure system-wide settings and preferences for the venue management system.</p>
    </div>
    
    <?php if (!empty($success_message)): ?>
        <div class="alert alert-success">
            <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
        </div>
    <?php endif; ?>
    
    <?php if (!empty($error_message)): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-circle"></i> <?php echo $error_message; ?>
        </div>
    <?php endif; ?>
    
    <?php if ($current_settings['maintenance_mode']): ?>
        <div class="maintenance-warning">
            <i class="fas fa-exclamation-triangle"></i> 
            <strong>Maintenance Mode is currently enabled.</strong> 
            Regular users cannot access the system.
        </div>
    <?php endif; ?>
    
    <div class="settings-grid">
        <div class="settings-card">
            <div class="card-header">
                <i class="fas fa-cogs"></i> General Settings
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="form-group">
                        <label for="system_name">System Name</label>
                        <input type="text" name="system_name" id="system_name" class="form-control" 
                               value="<?php echo htmlspecialchars($current_settings['system_name']); ?>" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="admin_email">Administrator Email</label>
                        <input type="email" name="admin_email" id="admin_email" class="form-control" 
                               value="<?php echo htmlspecialchars($current_settings['admin_email']); ?>" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="max_venues">Maximum Venues</label>
                        <input type="number" name="max_venues" id="max_venues" class="form-control" 
                               value="<?php echo $current_settings['max_venues']; ?>" min="1" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="session_timeout">Session Timeout (minutes)</label>
                        <input type="number" name="session_timeout" id="session_timeout" class="form-control" 
                               value="<?php echo $current_settings['session_timeout']; ?>" min="5" required>
                    </div>
                    
                    <div class="form-check">
                        <input type="checkbox" name="enable_notifications" id="enable_notifications" 
                               <?php echo $current_settings['enable_notifications'] ? 'checked' : ''; ?>>
                        <label for="enable_notifications">Enable Notifications</label>
                    </div>
                    
                    <div class="form-check">
                        <input type="checkbox" name="enable_qr_codes" id="enable_qr_codes" 
                               <?php echo $current_settings['enable_qr_codes'] ? 'checked' : ''; ?>>
                        <label for="enable_qr_codes">Enable QR Code Generation</label>
                    </div>
                    
                    <div class="form-check">
                        <input type="checkbox" name="maintenance_mode" id="maintenance_mode" 
                               <?php echo $current_settings['maintenance_mode'] ? 'checked' : ''; ?>>
                        <label for="maintenance_mode">Maintenance Mode</label>
                    </div>
                    
                    <button type="submit" name="update_settings" class="btn btn-primary">
                        <i class="fas fa-save"></i> Save Settings
                    </button>
                </form>
            </div>
        </div>
        
        <div>
            <div class="settings-card">
                <div class="card-header">
                    <i class="fas fa-chart-bar"></i> System Statistics
                </div>
                <div class="card-body">
                    <ul class="stats-list">
                        <li>
                            <span>Total Users</span>
                            <span class="stats-value"><?php echo $stats['total_users']; ?></span>
                        </li>
                        <li>
                            <span>Total Venues</span>
                            <span class="stats-value"><?php echo $stats['total_venues']; ?></span>
                        </li>
                        <li>
                            <span>Timetable Entries</span>
                            <span class="stats-value"><?php echo $stats['total_timetables']; ?></span>
                        </li>
                        <li>
                            <span>Notifications</span>
                            <span class="stats-value"><?php echo $stats['total_notifications']; ?></span>
                        </li>
                    </ul>
                </div>
            </div>
            
            <div class="settings-card" style="margin-top: 20px;">
                <div class="card-header">
                    <i class="fas fa-tools"></i> System Maintenance
                </div>
                <div class="card-body">
                    <form method="POST" style="margin-bottom: 15px;">
                        <button type="submit" name="backup_database" class="btn btn-warning" 
                                onclick="return confirm('Create a database backup? This may take a few moments.')">
                            <i class="fas fa-database"></i> Backup Database
                        </button>
                    </form>
                    
                    <form method="POST" style="margin-bottom: 15px;">
                        <button type="submit" name="clear_cache" class="btn btn-danger" 
                                onclick="return confirm('Clear system cache? This will remove all cached files.')">
                            <i class="fas fa-broom"></i> Clear Cache
                        </button>
                    </form>
                    
                    <p style="font-size: 14px; color: #666; margin-top: 15px;">
                        <i class="fas fa-info-circle"></i> 
                        Regular maintenance helps keep the system running smoothly.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

</body>
</html>
