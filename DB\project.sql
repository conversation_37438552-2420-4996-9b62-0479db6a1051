-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Jun 07, 2025 at 10:25 PM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `project`
--

-- --------------------------------------------------------

--
-- Table structure for table `password_reset_requests`
--

CREATE TABLE `password_reset_requests` (
  `id` int(10) UNSIGNED NOT NULL,
  `user_id` int(10) UNSIGNED NOT NULL,
  `token` varchar(64) NOT NULL,
  `expires_at` datetime NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `used` tinyint(1) NOT NULL DEFAULT 0,
  `full_name` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `reports`
--

CREATE TABLE `reports` (
  `report_id` int(11) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `venue_id` int(11) DEFAULT NULL,
  `description` text NOT NULL,
  `status` varchar(20) DEFAULT 'pending',
  `reported_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `reports`
--

INSERT INTO `reports` (`report_id`, `user_id`, `venue_id`, `description`, `status`, `reported_at`) VALUES
(1, 11, 17, 'LIGHTs are not working', 'resolved', '2025-05-26 07:59:52'),
(2, 2, 15, 'not working', 'resolved', '2025-05-26 08:00:34');

-- --------------------------------------------------------

--
-- Table structure for table `timetables`
--

CREATE TABLE `timetables` (
  `id` int(11) NOT NULL,
  `course` varchar(50) DEFAULT NULL,
  `year` int(11) DEFAULT NULL,
  `stream` varchar(10) DEFAULT NULL,
  `day` varchar(10) DEFAULT NULL,
  `start_time` time DEFAULT NULL,
  `end_time` time DEFAULT NULL,
  `type` varchar(30) DEFAULT NULL,
  `subject_code` varchar(50) DEFAULT NULL,
  `venue` varchar(50) DEFAULT NULL,
  `lecturer` varchar(100) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `timetables`
--

INSERT INTO `timetables` (`id`, `course`, `year`, `stream`, `day`, `start_time`, `end_time`, `type`, `subject_code`, `venue`, `lecturer`) VALUES
(1, 'BSc IT', 1, 'A', 'Monday', '09:00:00', '10:00:00', 'Lecture', 'ITU_07209', 'TH_A', 'Kwesigabo, E. (Dr)'),
(2, 'BSc IT', 1, 'A', 'Monday', '10:00:00', '11:00:00', 'Tutorial', 'G1_ITU 07208', 'LAB_5', 'Siphy, A. S (Mr)'),
(3, 'BSc IT', 1, 'B', 'Tuesday', '11:00:00', '12:00:00', 'Lecture', 'ITU_07207', 'TH_A', 'Bakiri, A.H (Dr)'),
(4, 'BSc IT', 1, 'B', 'Wednesday', '12:00:00', '13:00:00', 'Tutorial', 'G2_ITU 07209', 'LAB_4', 'Kwesigabo, E. (Dr)'),
(5, 'BSc IT', 2, 'A', 'Monday', '09:00:00', '10:00:00', 'Lecture', 'CSU_07426', 'TH_A', 'Nagunwa, T (Dr)'),
(6, 'BSc IT', 2, 'B', 'Tuesday', '10:00:00', '11:00:00', 'Practical_Lab', 'G2_ITU 07421', 'LAB_8', 'Ngowi, J (Mr)'),
(7, 'BSc IT', 2, 'A', 'Wednesday', '11:00:00', '12:00:00', 'Tutorial', 'G4_ITU 07425', 'LAB_8', 'Mutama, S (Mr)'),
(8, 'BSc IT', 2, 'B', 'Thursday', '13:00:00', '14:00:00', 'Lecture', 'ITU_07422', 'TH_B', 'Massa, D (Dr)'),
(9, 'BSc IT', 3, 'SysDev', 'Monday', '09:00:00', '10:00:00', 'Tutorial', 'ITU/CSU_08213', 'LAB_4', 'Shefa, F'),
(10, 'BSc IT', 3, 'SysAdmin', 'Tuesday', '10:00:00', '11:00:00', 'Lecture', 'CSU_08221', 'TH_B', 'Miku, H (Mr)'),
(11, 'BSc IT', 3, 'SysDev', 'Wednesday', '14:00:00', '15:00:00', 'Lecture', 'CSU_08220', 'TH_B', 'Mushi, R (Dr)'),
(12, 'ODIT', 1, 'A', 'Monday', '09:00:00', '10:00:00', 'Lecture', 'ITT_05211', 'LAB_1', 'Ngowi, J (Mr)'),
(13, 'ODIT', 1, 'A', 'Tuesday', '10:00:00', '11:00:00', 'Tutorial', 'ITT_05210', 'LAB_1', 'Ngeleshi, R (Mr)'),
(14, 'ODIT', 2, 'A', 'Wednesday', '09:00:00', '10:00:00', 'Lecture', 'ITT_06212', 'LAB_1', 'Byanyuma, M (Dr)'),
(15, 'ODIT', 2, 'A', 'Friday', '11:00:00', '12:00:00', 'Tutorial', 'ITT_06214', 'LAB_5', 'Bakiri, A.H (Dr)');

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `user_id` int(11) NOT NULL,
  `full_name` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `password` varchar(255) NOT NULL,
  `role` varchar(20) DEFAULT 'student',
  `course` varchar(50) DEFAULT NULL,
  `year` int(11) DEFAULT NULL,
  `stream` varchar(10) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`user_id`, `full_name`, `email`, `password`, `role`, `course`, `year`, `stream`, `created_at`) VALUES
(1, 'zorayz abdul', '<EMAIL>', '$2y$10$AUS4brmrTiWd9evYaVfkR.2FcHaLSdyjObRe1KRWu4LaXWmJ9EtxK', 'admin', '', NULL, NULL, '2025-05-01 10:00:00'),
(2, 'john doe', '<EMAIL>', '$2y$10$AUS4brmrTiWd9evYaVfkR.2FcHaLSdyjObRe1KRWu4LaXWmJ9EtxK', 'lecturer', '', NULL, NULL, '2025-05-02 11:00:00'),
(5, 'peter', '<EMAIL>', '$2y$10$AUS4brmrTiWd9evYaVfkR.2FcHaLSdyjObRe1KRWu4LaXWmJ9EtxK', 'student', '', NULL, NULL, '2025-05-03 12:00:00'),
(6, 'ISACK SWAI', '<EMAIL>', '$2y$10$5WXtBZ0UyAm2tojAF0bdmua2oKC0wV/aT0R3DO1qdZQFabKTbih8G', 'student', 'ODIT', 1, 'A', '2025-05-04 13:00:00'),
(7, 'likilo', '<EMAIL>', '$2y$10$APQURwAoCuRvqXIyBvL1K.i6yv1bhZh.cr5kTZbpIt8zTfNXMzErG', 'student', 'BSc IT', 2, 'A', '2025-05-05 14:00:00'),
(8, 'likilo', '<EMAIL>', '$2y$10$5l2mDD3gc8wN9x7smCkls.IUtH/43EiD1CjS66yn8O5YWsiwW5oEK', 'student', '0', 2, 'B', '2025-05-06 15:00:00'),
(10, 'tunu', '<EMAIL>', '$2y$10$u0F2QmhLLxNJ8L7b.zuJ8exfFmTCfrbpd/yU9py4JJ2BkL/C.n.H.', 'student', '0', 3, 'C', '2025-05-07 16:00:00'),
(11, 'Donatha', '<EMAIL>', '$2y$10$8bdg9.veAhPyRB7j86wApO6TUI6S3jUrvLimfrpnBcWC0b46C9.3.', 'student', 'ODIT', 2, 'B', '2025-05-08 17:00:00'),
(12, 'petro', '<EMAIL>', '$2y$10$c8LwPUdM59VY/jbuYPeMz.1MWmqc9mEN4H8w4KzMwW6q/O3yUK.N.', 'student', 'BSc IT', 1, 'B', '2025-05-09 18:00:00');

-- --------------------------------------------------------

--
-- Table structure for table `venues`
--

CREATE TABLE `venues` (
  `venueid` int(11) NOT NULL,
  `venuename` varchar(255) NOT NULL,
  `qrcode` varchar(255) NOT NULL,
  `venuedescription` text DEFAULT NULL,
  `status` enum('free','occupied') NOT NULL DEFAULT 'free',
  `capacity` int(11) DEFAULT NULL,
  `location` varchar(255) DEFAULT NULL,
  `building` varchar(100) DEFAULT NULL,
  `features` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `venues`
--

-- No venue data - venues table is empty

-- --------------------------------------------------------

--
-- Table structure for table `issues`
--

CREATE TABLE `issues` (
  `issue_id` int(11) NOT NULL,
  `venue_id` int(11) DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `issue_title` varchar(255) NOT NULL,
  `description` text NOT NULL,
  `issue_type` varchar(50) NOT NULL,
  `status` enum('pending','in_progress','resolved','closed') DEFAULT 'pending',
  `reported_date` datetime DEFAULT current_timestamp(),
  `resolved_date` datetime DEFAULT NULL,
  `resolved_by` int(11) DEFAULT NULL,
  `resolution_notes` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `notifications`
--

CREATE TABLE `notifications` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `type` varchar(20) DEFAULT 'info',
  `action_url` varchar(255) DEFAULT '',
  `is_read` tinyint(1) DEFAULT 0,
  `created_at` datetime DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `login_logs`
--

CREATE TABLE `login_logs` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `login_time` datetime DEFAULT current_timestamp(),
  `logout_time` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `venue_checkins`
--

CREATE TABLE `venue_checkins` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `venue_id` int(11) NOT NULL,
  `check_in_time` datetime DEFAULT current_timestamp(),
  `check_out_time` datetime DEFAULT NULL,
  `purpose` varchar(255) DEFAULT NULL,
  `notes` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Indexes for dumped tables
--

--
-- Indexes for table `password_reset_requests`
--
ALTER TABLE `password_reset_requests`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `token` (`token`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `reports`
--
ALTER TABLE `reports`
  ADD PRIMARY KEY (`report_id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `venue_id` (`venue_id`);

--
-- Indexes for table `timetables`
--
ALTER TABLE `timetables`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`user_id`);

--
-- Indexes for table `venues`
--
ALTER TABLE `venues`
  ADD PRIMARY KEY (`venueid`);

--
-- Indexes for table `issues`
--
ALTER TABLE `issues`
  ADD PRIMARY KEY (`issue_id`),
  ADD KEY `venue_id` (`venue_id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `resolved_by` (`resolved_by`);

--
-- Indexes for table `notifications`
--
ALTER TABLE `notifications`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `login_logs`
--
ALTER TABLE `login_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `venue_checkins`
--
ALTER TABLE `venue_checkins`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `venue_id` (`venue_id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `password_reset_requests`
--
ALTER TABLE `password_reset_requests`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `reports`
--
ALTER TABLE `reports`
  MODIFY `report_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `timetables`
--
ALTER TABLE `timetables`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=16;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `user_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=13;

--
-- AUTO_INCREMENT for table `venues`
--
ALTER TABLE `venues`
  MODIFY `venueid` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=20;

--
-- AUTO_INCREMENT for table `issues`
--
ALTER TABLE `issues`
  MODIFY `issue_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `notifications`
--
ALTER TABLE `notifications`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `login_logs`
--
ALTER TABLE `login_logs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `venue_checkins`
--
ALTER TABLE `venue_checkins`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `reports`
--
ALTER TABLE `reports`
  ADD CONSTRAINT `reports_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`),
  ADD CONSTRAINT `reports_ibfk_2` FOREIGN KEY (`venue_id`) REFERENCES `venues` (`venueid`);

--
-- Constraints for table `issues`
--
ALTER TABLE `issues`
  ADD CONSTRAINT `issues_ibfk_1` FOREIGN KEY (`venue_id`) REFERENCES `venues` (`venueid`) ON DELETE SET NULL,
  ADD CONSTRAINT `issues_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `issues_ibfk_3` FOREIGN KEY (`resolved_by`) REFERENCES `users` (`user_id`) ON DELETE SET NULL;

--
-- Constraints for table `notifications`
--
ALTER TABLE `notifications`
  ADD CONSTRAINT `notifications_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE;

--
-- Constraints for table `login_logs`
--
ALTER TABLE `login_logs`
  ADD CONSTRAINT `login_logs_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE;

--
-- Constraints for table `venue_checkins`
--
ALTER TABLE `venue_checkins`
  ADD CONSTRAINT `venue_checkins_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `venue_checkins_ibfk_2` FOREIGN KEY (`venue_id`) REFERENCES `venues` (`venueid`) ON DELETE CASCADE;

COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
