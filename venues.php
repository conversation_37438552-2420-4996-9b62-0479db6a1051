<?php
session_start();

// Check if the user is logged in and is an admin
if (!isset($_SESSION["user"]) || $_SESSION["role"] !== "admin") {
    // Redirect unauthorized users to login page
    header("Location: login.php");
    exit();
}

// Database connection
require_once "database.php";

// Handle venue deletion
if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    $venue_id = $_GET['delete'];
    $delete_sql = "DELETE FROM venues WHERE venueid = ?";
    $delete_stmt = mysqli_prepare($conn, $delete_sql);
    mysqli_stmt_bind_param($delete_stmt, "i", $venue_id);

    if (mysqli_stmt_execute($delete_stmt)) {
        $success_message = "Venue deleted successfully!";
    } else {
        $error_message = "Error deleting venue: " . mysqli_error($conn);
    }
}

// Handle venue status update
if (isset($_POST['update_status']) && isset($_POST['venue_id']) && isset($_POST['new_status'])) {
    $venue_id = $_POST['venue_id'];
    $new_status = $_POST['new_status'];

    $update_sql = "UPDATE venues SET status = ? WHERE venueid = ?";
    $update_stmt = mysqli_prepare($conn, $update_sql);
    mysqli_stmt_bind_param($update_stmt, "si", $new_status, $venue_id);

    if (mysqli_stmt_execute($update_stmt)) {
        $success_message = "Venue status updated successfully!";
    } else {
        $error_message = "Error updating venue status: " . mysqli_error($conn);
    }
}

// Fetch all venues
$venues_sql = "SELECT venueid, venuename, location, building, capacity, status, venuedescription, features FROM venues ORDER BY venuename ASC";
$venues_result = mysqli_query($conn, $venues_sql);

// Get venue statistics
$stats_sql = "SELECT
    COUNT(*) as total_venues,
    SUM(CASE WHEN status = 'free' THEN 1 ELSE 0 END) as free_count,
    SUM(CASE WHEN status = 'occupied' THEN 1 ELSE 0 END) as occupied_count,
    AVG(capacity) as avg_capacity
    FROM venues";
$stats_result = mysqli_query($conn, $stats_sql);
$stats = mysqli_fetch_assoc($stats_result);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <?php include('CSS/header.php'); ?>
    <title>Manage Venues | Admin Dashboard</title>
    <style>
        :root {
            --primary-color: #175883;
            --primary-dark: #0d3c60;
            --primary-light: #2980b9;
            --accent-color: #cbb09c;
            --text-light: #ffffff;
            --text-dark: #333333;
            --bg-light: #f8f9fa;
            --bg-dark: #2d3a4b;
            --success-color: #2ecc71;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --info-color: #3498db;
        }
        
        body {
            background-color: var(--bg-light);
            font-family: 'Roboto', sans-serif;
            margin: 0;
            padding: 0;
            color: var(--text-dark);
        }
        
        .container {
            padding: 30px;
            background-color: #f8f9fa;
            border-radius: 10px;
            box-shadow: 0 0 15px rgba(0,0,0,0.05);
            margin: 20px;
            transition: margin-left 0.3s ease;
        }
        
        #menuToggle:checked ~ .container {
            margin-left: 270px;
        }
        
        .page-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
            padding: 25px;
            border-radius: 8px;
            margin-bottom: 30px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .page-header h1 {
            margin: 0 0 10px 0;
            font-size: 28px;
            font-weight: 500;
        }
        
        .page-header p {
            margin: 0;
            font-size: 16px;
            opacity: 0.9;
        }
        
        .stats-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stat-card .stat-number {
            font-size: 32px;
            font-weight: bold;
            color: var(--primary-color);
            margin-bottom: 5px;
        }
        
        .stat-card .stat-label {
            color: #666;
            font-size: 14px;
        }
        
        .actions-bar {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 20px;
            gap: 15px;
        }
        
        .btn {
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s;
            border: none;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-primary {
            background: var(--primary-color);
            color: white;
        }
        
        .btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .btn-danger {
            background: var(--danger-color);
            color: white;
        }
        
        .btn-danger:hover {
            background: #c0392b;
        }
        
        .btn-sm {
            padding: 5px 10px;
            font-size: 12px;
        }
        
        .search-box {
            flex: 1;
            max-width: 300px;
        }
        
        .search-box input {
            width: 100%;
            padding: 10px 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .venues-table {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .table th {
            background: var(--primary-light);
            color: white;
            padding: 15px;
            text-align: left;
            font-weight: 500;
        }
        
        .table td {
            padding: 15px;
            border-bottom: 1px solid #eee;
        }
        
        .table tr:hover {
            background-color: rgba(41, 128, 185, 0.05);
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-free {
            background: rgba(46, 204, 113, 0.2);
            color: #2ecc71;
        }

        .status-occupied {
            background: rgba(231, 76, 60, 0.2);
            color: #e74c3c;
        }

        .capacity-info {
            font-size: 12px;
            color: #666;
        }

        .venue-features {
            font-size: 12px;
            color: #666;
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .alert {
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background-color: rgba(46, 204, 113, 0.2);
            border: 1px solid var(--success-color);
            color: #27ae60;
        }
        
        .alert-danger {
            background-color: rgba(231, 76, 60, 0.2);
            border: 1px solid var(--danger-color);
            color: #c0392b;
        }
        
        .status-select {
            padding: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 12px;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 15px;
                margin: 10px;
            }
            
            .stats-container {
                grid-template-columns: 1fr;
            }
            
            .actions-bar {
                flex-direction: column;
                align-items: stretch;
            }
            
            .search-box {
                max-width: none;
            }
            
            .table {
                font-size: 14px;
            }
            
            .table th,
            .table td {
                padding: 10px 8px;
            }
            
            #menuToggle:checked ~ .container {
                margin-left: 20px;
                opacity: 0.4;
            }
        }
    </style>
</head>
<body>

<?php include('CSS/sidebar.php'); ?>

<div class="container">
    <div class="page-header">
        <h1>Manage Venues</h1>
        <p>View and manage all venue spaces including lecture halls, laboratories, and meeting rooms.</p>
    </div>
    
    <?php if (isset($success_message)): ?>
        <div class="alert alert-success">
            <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($error_message)): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-circle"></i> <?php echo $error_message; ?>
        </div>
    <?php endif; ?>
    
    <div class="stats-container">
        <div class="stat-card">
            <div class="stat-number"><?php echo $stats['total_venues']; ?></div>
            <div class="stat-label">Total Venues</div>
        </div>
        <div class="stat-card">
            <div class="stat-number"><?php echo $stats['free_count']; ?></div>
            <div class="stat-label">Available</div>
        </div>
        <div class="stat-card">
            <div class="stat-number"><?php echo $stats['occupied_count']; ?></div>
            <div class="stat-label">Occupied</div>
        </div>
        <div class="stat-card">
            <div class="stat-number"><?php echo round($stats['avg_capacity']); ?></div>
            <div class="stat-label">Avg Capacity</div>
        </div>
    </div>
    
    <div class="actions-bar">
        <div class="search-box">
            <input type="text" id="searchInput" placeholder="Search venues..." onkeyup="searchVenues()">
        </div>
        <a href="add_venue.php" class="btn btn-primary">
            <i class="fas fa-plus"></i> Add New Venue
        </a>
    </div>
    
    <div class="venues-table">
        <table class="table" id="venuesTable">
            <thead>
                <tr>
                    <th>Venue Name</th>
                    <th>Location</th>
                    <th>Building</th>
                    <th>Capacity</th>
                    <th>Status</th>
                    <th>Features</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php if (mysqli_num_rows($venues_result) > 0): ?>
                    <?php while ($venue = mysqli_fetch_assoc($venues_result)): ?>
                        <tr>
                            <td>
                                <strong><?php echo htmlspecialchars($venue['venuename']); ?></strong>
                                <?php if (!empty($venue['venuedescription'])): ?>
                                    <br><small style="color: #666;"><?php echo htmlspecialchars($venue['venuedescription']); ?></small>
                                <?php endif; ?>
                            </td>
                            <td><?php echo htmlspecialchars($venue['location']); ?></td>
                            <td><?php echo htmlspecialchars($venue['building']); ?></td>
                            <td>
                                <span class="capacity-info"><?php echo $venue['capacity']; ?> people</span>
                            </td>
                            <td>
                                <span class="status-badge status-<?php echo $venue['status']; ?>">
                                    <?php echo ucfirst($venue['status']); ?>
                                </span>
                            </td>
                            <td>
                                <span class="venue-features" title="<?php echo htmlspecialchars($venue['features']); ?>">
                                    <?php echo htmlspecialchars($venue['features']); ?>
                                </span>
                            </td>
                            <td>
                                <form method="post" style="display: inline-block; margin-right: 10px;">
                                    <input type="hidden" name="venue_id" value="<?php echo $venue['venueid']; ?>">
                                    <select name="new_status" class="status-select" onchange="this.form.submit()">
                                        <option value="free" <?php echo ($venue['status'] === 'free') ? 'selected' : ''; ?>>Free</option>
                                        <option value="occupied" <?php echo ($venue['status'] === 'occupied') ? 'selected' : ''; ?>>Occupied</option>
                                    </select>
                                    <input type="hidden" name="update_status" value="1">
                                </form>
                                <a href="edit_venue.php?id=<?php echo $venue['venueid']; ?>"
                                   class="btn btn-secondary btn-sm"
                                   title="Edit venue">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="qr_code.php?venue_id=<?php echo $venue['venueid']; ?>"
                                   class="btn btn-primary btn-sm"
                                   title="View QR Code">
                                    <i class="fas fa-qrcode"></i>
                                </a>
                                <a href="?delete=<?php echo $venue['venueid']; ?>"
                                   class="btn btn-danger btn-sm"
                                   onclick="return confirm('Are you sure you want to delete this venue?')"
                                   title="Delete venue">
                                    <i class="fas fa-trash"></i>
                                </a>
                            </td>
                        </tr>
                    <?php endwhile; ?>
                <?php else: ?>
                    <tr>
                        <td colspan="7" style="text-align: center; padding: 40px; color: #666;">
                            <i class="fas fa-building" style="font-size: 48px; margin-bottom: 15px; opacity: 0.5;"></i>
                            <br>No venues found
                        </td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>

<script>
    function searchVenues() {
        const input = document.getElementById('searchInput');
        const filter = input.value.toLowerCase();
        const table = document.getElementById('venuesTable');
        const rows = table.getElementsByTagName('tr');

        for (let i = 1; i < rows.length; i++) {
            const cells = rows[i].getElementsByTagName('td');
            let found = false;

            for (let j = 0; j < cells.length - 1; j++) {
                if (cells[j].textContent.toLowerCase().includes(filter)) {
                    found = true;
                    break;
                }
            }

            rows[i].style.display = found ? '' : 'none';
        }
    }
</script>

</body>
</html>
