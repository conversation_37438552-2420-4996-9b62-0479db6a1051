<?php
session_start();

// Check if the user is logged in and is an admin
if (!isset($_SESSION["user"]) || $_SESSION["role"] !== "admin") {
    // Redirect unauthorized users to login page
    header("Location: login.php");
    exit();
}

// Database connection
require_once "database.php";

// Handle timetable entry deletion
if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    $timetable_id = $_GET['delete'];
    $delete_sql = "DELETE FROM timetables WHERE id = ?";
    $delete_stmt = mysqli_prepare($conn, $delete_sql);
    mysqli_stmt_bind_param($delete_stmt, "i", $timetable_id);
    
    if (mysqli_stmt_execute($delete_stmt)) {
        $success_message = "Timetable entry deleted successfully!";
    } else {
        $error_message = "Error deleting timetable entry: " . mysqli_error($conn);
    }
}

// Handle bulk delete
if (isset($_POST['bulk_delete']) && isset($_POST['selected_entries'])) {
    $selected_entries = $_POST['selected_entries'];
    $placeholders = str_repeat('?,', count($selected_entries) - 1) . '?';
    $delete_sql = "DELETE FROM timetables WHERE id IN ($placeholders)";
    $delete_stmt = mysqli_prepare($conn, $delete_sql);
    
    $types = str_repeat('i', count($selected_entries));
    mysqli_stmt_bind_param($delete_stmt, $types, ...$selected_entries);
    
    if (mysqli_stmt_execute($delete_stmt)) {
        $success_message = count($selected_entries) . " timetable entries deleted successfully!";
    } else {
        $error_message = "Error deleting timetable entries: " . mysqli_error($conn);
    }
}

// Get filter parameters
$course_filter = isset($_GET['course']) ? $_GET['course'] : '';
$year_filter = isset($_GET['year']) ? $_GET['year'] : '';
$day_filter = isset($_GET['day']) ? $_GET['day'] : '';

// Build the query with filters
$where_conditions = [];
$params = [];
$types = '';

if (!empty($course_filter)) {
    $where_conditions[] = "course LIKE ?";
    $params[] = "%$course_filter%";
    $types .= 's';
}

if (!empty($year_filter)) {
    $where_conditions[] = "year = ?";
    $params[] = $year_filter;
    $types .= 'i';
}

if (!empty($day_filter)) {
    $where_conditions[] = "day = ?";
    $params[] = $day_filter;
    $types .= 's';
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Fetch timetable entries
$timetables_sql = "SELECT id, course, year, stream, day, start_time, end_time, type, subject_code, venue, lecturer 
                   FROM timetables $where_clause 
                   ORDER BY 
                   CASE day 
                       WHEN 'Monday' THEN 1
                       WHEN 'Tuesday' THEN 2
                       WHEN 'Wednesday' THEN 3
                       WHEN 'Thursday' THEN 4
                       WHEN 'Friday' THEN 5
                       WHEN 'Saturday' THEN 6
                       WHEN 'Sunday' THEN 7
                   END, start_time";

$timetables_stmt = mysqli_prepare($conn, $timetables_sql);

if (!empty($params)) {
    mysqli_stmt_bind_param($timetables_stmt, $types, ...$params);
}

mysqli_stmt_execute($timetables_stmt);
$timetables_result = mysqli_stmt_get_result($timetables_stmt);

// Get statistics
$stats_sql = "SELECT 
    COUNT(*) as total_entries,
    COUNT(DISTINCT course) as total_courses,
    COUNT(DISTINCT venue) as total_venues,
    COUNT(DISTINCT lecturer) as total_lecturers
    FROM timetables";
$stats_result = mysqli_query($conn, $stats_sql);
$stats = mysqli_fetch_assoc($stats_result);

// Get unique values for filters
$courses_sql = "SELECT DISTINCT course FROM timetables ORDER BY course";
$courses_result = mysqli_query($conn, $courses_sql);

$years_sql = "SELECT DISTINCT year FROM timetables ORDER BY year";
$years_result = mysqli_query($conn, $years_sql);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <?php include('CSS/header.php'); ?>
    <title>Manage Timetables | Admin Dashboard</title>
    <style>
        :root {
            --primary-color: #175883;
            --primary-dark: #0d3c60;
            --primary-light: #2980b9;
            --accent-color: #cbb09c;
            --text-light: #ffffff;
            --text-dark: #333333;
            --bg-light: #f8f9fa;
            --bg-dark: #2d3a4b;
            --success-color: #2ecc71;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --info-color: #3498db;
        }
        
        body {
            background-color: var(--bg-light);
            font-family: 'Roboto', sans-serif;
            margin: 0;
            padding: 0;
            color: var(--text-dark);
        }
        
        .container {
            padding: 30px;
            background-color: #f8f9fa;
            border-radius: 10px;
            box-shadow: 0 0 15px rgba(0,0,0,0.05);
            margin: 20px;
            transition: margin-left 0.3s ease;
        }
        
        #menuToggle:checked ~ .container {
            margin-left: 270px;
        }
        
        .page-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
            padding: 25px;
            border-radius: 8px;
            margin-bottom: 30px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .page-header h1 {
            margin: 0 0 10px 0;
            font-size: 28px;
            font-weight: 500;
        }
        
        .page-header p {
            margin: 0;
            font-size: 16px;
            opacity: 0.9;
        }
        
        .stats-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stat-card .stat-number {
            font-size: 32px;
            font-weight: bold;
            color: var(--primary-color);
            margin-bottom: 5px;
        }
        
        .stat-card .stat-label {
            color: #666;
            font-size: 14px;
        }
        
        .filters-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .filters-form {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            align-items: end;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
        }
        
        .form-group label {
            margin-bottom: 5px;
            font-weight: 500;
            color: var(--text-dark);
        }
        
        .form-control {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .btn {
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s;
            border: none;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-primary {
            background: var(--primary-color);
            color: white;
        }
        
        .btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .btn-danger {
            background: var(--danger-color);
            color: white;
        }
        
        .btn-danger:hover {
            background: #c0392b;
        }
        
        .btn-sm {
            padding: 5px 10px;
            font-size: 12px;
        }
        
        .actions-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            gap: 15px;
        }
        
        .timetables-table {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .table th {
            background: var(--primary-light);
            color: white;
            padding: 15px 10px;
            text-align: left;
            font-weight: 500;
            font-size: 14px;
        }
        
        .table td {
            padding: 12px 10px;
            border-bottom: 1px solid #eee;
            font-size: 14px;
        }
        
        .table tr:hover {
            background-color: rgba(41, 128, 185, 0.05);
        }
        
        .type-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
        }
        
        .type-lecture {
            background: rgba(52, 152, 219, 0.2);
            color: #3498db;
        }
        
        .type-practical {
            background: rgba(46, 204, 113, 0.2);
            color: #2ecc71;
        }
        
        .type-tutorial {
            background: rgba(243, 156, 18, 0.2);
            color: #f39c12;
        }
        
        .alert {
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background-color: rgba(46, 204, 113, 0.2);
            border: 1px solid var(--success-color);
            color: #27ae60;
        }
        
        .alert-danger {
            background-color: rgba(231, 76, 60, 0.2);
            border: 1px solid var(--danger-color);
            color: #c0392b;
        }
        
        .bulk-actions {
            display: none;
            background: #fff3cd;
            padding: 10px 15px;
            border-radius: 4px;
            margin-bottom: 15px;
            border: 1px solid #ffeaa7;
        }
        
        .bulk-actions.show {
            display: block;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 15px;
                margin: 10px;
            }
            
            .stats-container {
                grid-template-columns: 1fr;
            }
            
            .filters-form {
                grid-template-columns: 1fr;
            }
            
            .actions-bar {
                flex-direction: column;
                align-items: stretch;
            }
            
            .table {
                font-size: 12px;
            }
            
            .table th,
            .table td {
                padding: 8px 5px;
            }
            
            #menuToggle:checked ~ .container {
                margin-left: 20px;
                opacity: 0.4;
            }
        }
    </style>
</head>
<body>

<?php include('CSS/sidebar.php'); ?>

<div class="container">
    <div class="page-header">
        <h1>Manage Timetables</h1>
        <p>View and manage class schedules, lectures, and academic timetables.</p>
    </div>
    
    <?php if (isset($success_message)): ?>
        <div class="alert alert-success">
            <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($error_message)): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-circle"></i> <?php echo $error_message; ?>
        </div>
    <?php endif; ?>
    
    <div class="stats-container">
        <div class="stat-card">
            <div class="stat-number"><?php echo $stats['total_entries']; ?></div>
            <div class="stat-label">Total Entries</div>
        </div>
        <div class="stat-card">
            <div class="stat-number"><?php echo $stats['total_courses']; ?></div>
            <div class="stat-label">Courses</div>
        </div>
        <div class="stat-card">
            <div class="stat-number"><?php echo $stats['total_venues']; ?></div>
            <div class="stat-label">Venues</div>
        </div>
        <div class="stat-card">
            <div class="stat-number"><?php echo $stats['total_lecturers']; ?></div>
            <div class="stat-label">Lecturers</div>
        </div>
    </div>
    
    <div class="filters-section">
        <form method="GET" class="filters-form">
            <div class="form-group">
                <label for="course">Course</label>
                <select name="course" id="course" class="form-control">
                    <option value="">All Courses</option>
                    <?php while ($course = mysqli_fetch_assoc($courses_result)): ?>
                        <option value="<?php echo htmlspecialchars($course['course']); ?>" 
                                <?php echo ($course_filter === $course['course']) ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($course['course']); ?>
                        </option>
                    <?php endwhile; ?>
                </select>
            </div>
            
            <div class="form-group">
                <label for="year">Year</label>
                <select name="year" id="year" class="form-control">
                    <option value="">All Years</option>
                    <?php while ($year = mysqli_fetch_assoc($years_result)): ?>
                        <option value="<?php echo $year['year']; ?>" 
                                <?php echo ($year_filter == $year['year']) ? 'selected' : ''; ?>>
                            Year <?php echo $year['year']; ?>
                        </option>
                    <?php endwhile; ?>
                </select>
            </div>
            
            <div class="form-group">
                <label for="day">Day</label>
                <select name="day" id="day" class="form-control">
                    <option value="">All Days</option>
                    <option value="Monday" <?php echo ($day_filter === 'Monday') ? 'selected' : ''; ?>>Monday</option>
                    <option value="Tuesday" <?php echo ($day_filter === 'Tuesday') ? 'selected' : ''; ?>>Tuesday</option>
                    <option value="Wednesday" <?php echo ($day_filter === 'Wednesday') ? 'selected' : ''; ?>>Wednesday</option>
                    <option value="Thursday" <?php echo ($day_filter === 'Thursday') ? 'selected' : ''; ?>>Thursday</option>
                    <option value="Friday" <?php echo ($day_filter === 'Friday') ? 'selected' : ''; ?>>Friday</option>
                    <option value="Saturday" <?php echo ($day_filter === 'Saturday') ? 'selected' : ''; ?>>Saturday</option>
                    <option value="Sunday" <?php echo ($day_filter === 'Sunday') ? 'selected' : ''; ?>>Sunday</option>
                </select>
            </div>
            
            <div class="form-group">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-filter"></i> Filter
                </button>
            </div>
        </form>
    </div>
    
    <div class="actions-bar">
        <div>
            <a href="upload_timetable.php" class="btn btn-primary">
                <i class="fas fa-file-upload"></i> Upload Timetable
            </a>
        </div>
        <div>
            <button type="button" class="btn btn-danger" id="bulkDeleteBtn" style="display: none;">
                <i class="fas fa-trash"></i> Delete Selected
            </button>
        </div>
    </div>
    
    <form method="POST" id="bulkForm">
        <div class="bulk-actions" id="bulkActions">
            <span id="selectedCount">0</span> entries selected
            <button type="submit" name="bulk_delete" class="btn btn-danger btn-sm" 
                    onclick="return confirm('Are you sure you want to delete the selected entries?')">
                <i class="fas fa-trash"></i> Delete Selected
            </button>
        </div>
        
        <div class="timetables-table">
            <table class="table" id="timetablesTable">
                <thead>
                    <tr>
                        <th><input type="checkbox" id="selectAll"></th>
                        <th>Course</th>
                        <th>Year/Stream</th>
                        <th>Day</th>
                        <th>Time</th>
                        <th>Type</th>
                        <th>Subject</th>
                        <th>Venue</th>
                        <th>Lecturer</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (mysqli_num_rows($timetables_result) > 0): ?>
                        <?php while ($timetable = mysqli_fetch_assoc($timetables_result)): ?>
                            <tr>
                                <td>
                                    <input type="checkbox" name="selected_entries[]" 
                                           value="<?php echo $timetable['id']; ?>" class="entry-checkbox">
                                </td>
                                <td><?php echo htmlspecialchars($timetable['course']); ?></td>
                                <td>Year <?php echo $timetable['year']; ?> (<?php echo htmlspecialchars($timetable['stream']); ?>)</td>
                                <td><?php echo htmlspecialchars($timetable['day']); ?></td>
                                <td><?php echo htmlspecialchars($timetable['start_time'] . ' - ' . $timetable['end_time']); ?></td>
                                <td>
                                    <span class="type-badge type-<?php echo strtolower($timetable['type']); ?>">
                                        <?php echo ucfirst($timetable['type']); ?>
                                    </span>
                                </td>
                                <td><?php echo htmlspecialchars($timetable['subject_code']); ?></td>
                                <td><?php echo htmlspecialchars($timetable['venue']); ?></td>
                                <td><?php echo htmlspecialchars($timetable['lecturer']); ?></td>
                                <td>
                                    <a href="?delete=<?php echo $timetable['id']; ?>" 
                                       class="btn btn-danger btn-sm" 
                                       onclick="return confirm('Are you sure you want to delete this entry?')">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </td>
                            </tr>
                        <?php endwhile; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="10" style="text-align: center; padding: 40px; color: #666;">
                                <i class="fas fa-calendar-alt" style="font-size: 48px; margin-bottom: 15px; opacity: 0.5;"></i>
                                <br>No timetable entries found
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </form>
</div>

<script>
    // Handle select all checkbox
    document.getElementById('selectAll').addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('.entry-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateBulkActions();
    });
    
    // Handle individual checkboxes
    document.querySelectorAll('.entry-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', updateBulkActions);
    });
    
    function updateBulkActions() {
        const checkedBoxes = document.querySelectorAll('.entry-checkbox:checked');
        const bulkActions = document.getElementById('bulkActions');
        const selectedCount = document.getElementById('selectedCount');
        
        if (checkedBoxes.length > 0) {
            bulkActions.classList.add('show');
            selectedCount.textContent = checkedBoxes.length;
        } else {
            bulkActions.classList.remove('show');
        }
    }
</script>

</body>
</html>
