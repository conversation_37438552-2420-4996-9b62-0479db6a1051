<?php
session_start();

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Check if the user is logged in and is an admin
if (!isset($_SESSION["user"]) || $_SESSION["role"] !== "admin") {
    // Redirect unauthorized users to login page
    header("Location: login.php");
    exit();
}

// Database connection
require_once "database.php";

// QR code functionality is optional - will be handled without external libraries

// Initialize variables
$venuename = $capacity = $location = $building = $description = $features = "";
$venuename_err = $capacity_err = $location_err = "";
$success_message = "";

// Process form data when form is submitted
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    
    // Validate venue name
    if (empty(trim($_POST["venuename"]))) {
        $venuename_err = "Please enter a venue name.";
    } else {
        // Prepare a select statement
        $sql = "SELECT venueid FROM venues WHERE venuename = ?";
        
        if ($stmt = mysqli_prepare($conn, $sql)) {
            // Bind variables to the prepared statement as parameters
            mysqli_stmt_bind_param($stmt, "s", $param_venuename);
            
            // Set parameters
            $param_venuename = trim($_POST["venuename"]);
            
            // Attempt to execute the prepared statement
            if (mysqli_stmt_execute($stmt)) {
                // Store result
                mysqli_stmt_store_result($stmt);
                
                if (mysqli_stmt_num_rows($stmt) == 1) {
                    $venuename_err = "This venue name already exists.";
                } else {
                    $venuename = trim($_POST["venuename"]);
                }
            } else {
                echo "Oops! Something went wrong. Please try again later.";
            }

            // Close statement
            mysqli_stmt_close($stmt);
        }
    }
    
    // Validate capacity
    if (empty(trim($_POST["capacity"]))) {
        $capacity_err = "Please enter the venue capacity.";
    } elseif (!is_numeric(trim($_POST["capacity"])) || intval(trim($_POST["capacity"])) <= 0) {
        $capacity_err = "Capacity must be a positive number.";
    } else {
        $capacity = trim($_POST["capacity"]);
    }
    
    // Validate location
    if (empty(trim($_POST["location"]))) {
        $location_err = "Please enter the venue location.";
    } else {
        $location = trim($_POST["location"]);
    }
    
    // Get building, description and features (optional)
    $building = trim($_POST["building"]);
    $description = trim($_POST["description"]);
    $features = isset($_POST["features"]) ? implode(", ", $_POST["features"]) : "";
    
    // Check input errors before inserting into database
    if (empty($venuename_err) && empty($capacity_err) && empty($location_err)) {
        
        // Prepare an insert statement - include qrcode field since it's NOT NULL
        $sql = "INSERT INTO venues (venuename, qrcode, capacity, location, building, venuedescription, features, status) VALUES (?, ?, ?, ?, ?, ?, ?, 'free')";

        if ($stmt = mysqli_prepare($conn, $sql)) {
            // Create a placeholder QR code path
            $qr_placeholder = "qr_venue_" . time() . ".png";

            // Bind variables to the prepared statement as parameters
            mysqli_stmt_bind_param($stmt, "ssissss", $param_venuename, $param_qrcode, $param_capacity, $param_location, $param_building, $param_description, $param_features);

            // Set parameters
            $param_venuename = $venuename;
            $param_qrcode = $qr_placeholder;
            $param_capacity = $capacity;
            $param_location = $location;
            $param_building = $building;
            $param_description = $description;
            $param_features = $features;
            
            // Attempt to execute the prepared statement
            if (mysqli_stmt_execute($stmt)) {
                // Venue created successfully
                $venue_id = mysqli_insert_id($conn);

                $success_message = "Venue created successfully! Venue ID: " . $venue_id;

                // Clear form data
                $venuename = $capacity = $location = $building = $description = $features = "";
            } else {
                $error_message = "Database error: " . mysqli_error($conn);
            }

            // Close statement
            mysqli_stmt_close($stmt);
        }
    }
}

// Get locations for dropdown
$locations = [];
$sql = "SELECT DISTINCT location FROM venues WHERE location IS NOT NULL AND location != '' ORDER BY location";
$result = mysqli_query($conn, $sql);
if ($result) {
    while ($row = mysqli_fetch_assoc($result)) {
        $locations[] = $row['location'];
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <?php include('CSS/header.php'); ?>
    <title>Add New Venue | Admin Dashboard</title>
    <style>
        :root {
            --primary-color: #175883;
            --primary-dark: #0d3c60;
            --primary-light: #2980b9;
            --accent-color: #cbb09c;
            --text-light: #ffffff;
            --text-dark: #333333;
            --bg-light: #f8f9fa;
            --bg-dark: #2d3a4b;
            --success-color: #2ecc71;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --info-color: #3498db;
        }
        
        body {
            background-color: var(--bg-light);
            font-family: 'Roboto', sans-serif;
            margin: 0;
            padding: 0;
            color: var(--text-dark);
        }
        
        .container {
            padding: 30px;
            background-color: #f8f9fa;
            border-radius: 10px;
            box-shadow: 0 0 15px rgba(0,0,0,0.05);
            margin: 20px;
            transition: margin-left 0.3s ease;
        }
        
        #menuToggle:checked ~ .container {
            margin-left: 270px;
        }
        
        .page-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
            padding: 25px;
            border-radius: 8px;
            margin-bottom: 30px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .page-header h1 {
            margin: 0 0 10px 0;
            font-size: 28px;
            font-weight: 500;
        }
        
        .page-header p {
            margin: 0;
            font-size: 16px;
            opacity: 0.9;
        }
        
        .card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-bottom: 20px;
        }
        
        .card-header {
            background: var(--primary-light);
            color: white;
            padding: 15px 20px;
            font-size: 18px;
            font-weight: 500;
        }
        
        .card-body {
            padding: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: var(--text-dark);
        }
        
        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            transition: all 0.3s;
            box-sizing: border-box;
        }
        
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(23, 88, 131, 0.2);
        }
        
        .form-control.is-invalid {
            border-color: var(--danger-color);
        }
        
        .invalid-feedback {
            color: var(--danger-color);
            font-size: 14px;
            margin-top: 5px;
        }
        
        .btn {
            padding: 12px 25px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s;
            border: none;
        }
        
        .btn-primary {
            background: var(--primary-color);
            color: white;
        }
        
        .btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .alert {
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background-color: rgba(46, 204, 113, 0.2);
            border: 1px solid var(--success-color);
            color: #27ae60;
        }

        .alert-danger {
            background-color: rgba(231, 76, 60, 0.2);
            border: 1px solid var(--danger-color);
            color: #c0392b;
        }
        
        .checkbox-group {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 10px;
        }
        
        .checkbox-item {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .checkbox-item input[type="checkbox"] {
            margin-right: 10px;
        }
        
        textarea.form-control {
            min-height: 120px;
            resize: vertical;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 15px;
                margin: 10px;
            }
            
            .page-header {
                padding: 20px;
            }
            
            .page-header h1 {
                font-size: 24px;
            }
            
            .checkbox-group {
                grid-template-columns: 1fr;
            }
            
            #menuToggle:checked ~ .container {
                margin-left: 20px;
                opacity: 0.4;
            }
        }
    </style>
</head>
<body>

<?php include('CSS/sidebar.php'); ?>

<div class="container">
    <div class="page-header">
        <h1>Add New Venue</h1>
        <p>Create a new venue in the venue management system.</p>
    </div>
    
    <?php if (!empty($success_message)): ?>
        <div class="alert alert-success">
            <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
        </div>
    <?php endif; ?>

    <?php if (isset($error_message)): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-circle"></i> <?php echo $error_message; ?>
        </div>
    <?php endif; ?>
    
    <div class="card">
        <div class="card-header">
            <i class="fas fa-map-marker-alt"></i> Venue Registration Form
        </div>
        <div class="card-body">
            <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" method="post">
                <div class="form-group">
                    <label for="venuename">Venue Name</label>
                    <input type="text" name="venuename" id="venuename" class="form-control <?php echo (!empty($venuename_err)) ? 'is-invalid' : ''; ?>" value="<?php echo $venuename; ?>">
                    <?php if (!empty($venuename_err)): ?>
                        <div class="invalid-feedback"><?php echo $venuename_err; ?></div>
                    <?php endif; ?>
                </div>
                
                <div class="form-group">
                    <label for="capacity">Capacity</label>
                    <input type="number" name="capacity" id="capacity" class="form-control <?php echo (!empty($capacity_err)) ? 'is-invalid' : ''; ?>" value="<?php echo $capacity; ?>">
                    <?php if (!empty($capacity_err)): ?>
                        <div class="invalid-feedback"><?php echo $capacity_err; ?></div>
                    <?php endif; ?>
                </div>
                
                <div class="form-group">
                    <label for="location">Location</label>
                    <input type="text" name="location" id="location" class="form-control <?php echo (!empty($location_err)) ? 'is-invalid' : ''; ?>" value="<?php echo $location; ?>" list="location-list">
                    <datalist id="location-list">
                        <?php foreach ($locations as $location_option): ?>
                            <option value="<?php echo htmlspecialchars($location_option); ?>">
                        <?php endforeach; ?>
                    </datalist>
                    <?php if (!empty($location_err)): ?>
                        <div class="invalid-feedback"><?php echo $location_err; ?></div>
                    <?php endif; ?>
                </div>

                <div class="form-group">
                    <label for="building">Building</label>
                    <input type="text" name="building" id="building" class="form-control" value="<?php echo isset($building) ? $building : ''; ?>" placeholder="e.g., TT Building, Rafiki Building">
                </div>

                <div class="form-group">
                    <label for="description">Description</label>
                    <textarea name="description" id="description" class="form-control"><?php echo $description; ?></textarea>
                </div>
                
                <div class="form-group">
                    <label>Features</label>
                    <div class="checkbox-group">
                        <div class="checkbox-item">
                            <input type="checkbox" name="features[]" id="feature-projector" value="Projector">
                            <label for="feature-projector">Projector</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" name="features[]" id="feature-whiteboard" value="Whiteboard">
                            <label for="feature-whiteboard">Whiteboard</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" name="features[]" id="feature-ac" value="Air Conditioning">
                            <label for="feature-ac">Air Conditioning</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" name="features[]" id="feature-computers" value="Computers">
                            <label for="feature-computers">Computers</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" name="features[]" id="feature-audio" value="Audio System">
                            <label for="feature-audio">Audio System</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" name="features[]" id="feature-wifi" value="WiFi">
                            <label for="feature-wifi">WiFi</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" name="features[]" id="feature-accessible" value="Wheelchair Accessible">
                            <label for="feature-accessible">Wheelchair Accessible</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" name="features[]" id="feature-power" value="Power Outlets">
                            <label for="feature-power">Power Outlets</label>
                        </div>
                    </div>
                </div>
                
                <div class="form-group">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-plus-circle"></i> Create Venue
                    </button>
                    <a href="venues.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Venues
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

<?php include('CSS/footer.php'); ?>

<script>
    // JavaScript to handle form validation and UI enhancements
    document.addEventListener('DOMContentLoaded', function() {
        // You can add client-side validation here if needed
    });
</script>

</body>
</html>
