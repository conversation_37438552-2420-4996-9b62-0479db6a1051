﻿<?php
session_start();

// Check if the user is logged in and is an admin
if (!isset($_SESSION["user"]) || $_SESSION["role"] !== "admin") {
    header("Location: login.php");
    exit();
}

// Database connection
require_once "database.php";

// Get venue ID from URL
$venue_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($venue_id <= 0) {
    header("Location: venues.php");
    exit();
}

// Fetch venue details
$venue_sql = "SELECT * FROM venues WHERE venueid = ?";
$venue_stmt = mysqli_prepare($conn, $venue_sql);
mysqli_stmt_bind_param($venue_stmt, "i", $venue_id);
mysqli_stmt_execute($venue_stmt);
$venue_result = mysqli_stmt_get_result($venue_stmt);
$venue = mysqli_fetch_assoc($venue_result);

if (!$venue) {
    header("Location: venues.php");
    exit();
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $venuename = trim($_POST['venuename']);
    $location = trim($_POST['location']);
    $building = trim($_POST['building']);
    $capacity = (int)$_POST['capacity'];
    $status = $_POST['status'];
    $venuedescription = trim($_POST['venuedescription']);
    $features = trim($_POST['features']);

    if (!empty($venuename) && !empty($location) && !empty($building) && $capacity > 0) {
        $update_sql = "UPDATE venues SET venuename = ?, location = ?, building = ?, capacity = ?, status = ?, venuedescription = ?, features = ? WHERE venueid = ?";
        $update_stmt = mysqli_prepare($conn, $update_sql);
        mysqli_stmt_bind_param($update_stmt, "sssisssi", $venuename, $location, $building, $capacity, $status, $venuedescription, $features, $venue_id);

        if (mysqli_stmt_execute($update_stmt)) {
            $success_message = "Venue updated successfully!";
            // Refresh venue data
            mysqli_stmt_execute($venue_stmt);
            $venue_result = mysqli_stmt_get_result($venue_stmt);
            $venue = mysqli_fetch_assoc($venue_result);
        } else {
            $error_message = "Error updating venue: " . mysqli_error($conn);
        }
    } else {
        $error_message = "Please fill in all required fields.";
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <?php include('CSS/header.php'); ?>
    <title>Edit Venue | Admin Dashboard</title>
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Roboto', sans-serif;
        }

        .container {
            max-width: 800px;
            margin: 20px auto;
            padding: 30px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 0 15px rgba(0,0,0,0.1);
        }

        .page-header {
            background: linear-gradient(135deg, #175883, #0d3c60);
            color: white;
            padding: 25px;
            border-radius: 8px;
            margin-bottom: 30px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #333;
        }

        .form-control {
            width: 100%;
            padding: 10px 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }

        .form-control:focus {
            border-color: #175883;
            outline: none;
            box-shadow: 0 0 0 2px rgba(23, 88, 131, 0.2);
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            text-decoration: none;
            display: inline-block;
            margin-right: 10px;
        }

        .btn-primary {
            background: #175883;
            color: white;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn:hover {
            opacity: 0.9;
            transform: translateY(-1px);
        }

        .alert {
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }

        .alert-success {
            background-color: rgba(46, 204, 113, 0.2);
            border: 1px solid #2ecc71;
            color: #27ae60;
        }

        .alert-danger {
            background-color: rgba(231, 76, 60, 0.2);
            border: 1px solid #e74c3c;
            color: #c0392b;
        }

        .row {
            display: flex;
            gap: 20px;
        }

        .col-half {
            flex: 1;
        }

        textarea.form-control {
            min-height: 100px;
            resize: vertical;
        }
    </style>
</head>
<body>

<div class="container">
    <div class="page-header">
        <h1>Edit Venue</h1>
        <p>Update venue information and settings</p>
    </div>

    <?php if (isset($success_message)): ?>
        <div class="alert alert-success">
            <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
        </div>
    <?php endif; ?>

    <?php if (isset($error_message)): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-circle"></i> <?php echo $error_message; ?>
        </div>
    <?php endif; ?>

    <form method="POST">
        <div class="row">
            <div class="col-half">
                <div class="form-group">
                    <label for="venuename">Venue Name *</label>
                    <input type="text" id="venuename" name="venuename" class="form-control"
                           value="<?php echo htmlspecialchars($venue['venuename']); ?>" required>
                </div>
            </div>
            <div class="col-half">
                <div class="form-group">
                    <label for="status">Status</label>
                    <select id="status" name="status" class="form-control">
                        <option value="free" <?php echo ($venue['status'] === 'free') ? 'selected' : ''; ?>>Free</option>
                        <option value="occupied" <?php echo ($venue['status'] === 'occupied') ? 'selected' : ''; ?>>Occupied</option>
                    </select>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-half">
                <div class="form-group">
                    <label for="location">Location *</label>
                    <input type="text" id="location" name="location" class="form-control"
                           value="<?php echo htmlspecialchars($venue['location']); ?>" required>
                </div>
            </div>
            <div class="col-half">
                <div class="form-group">
                    <label for="building">Building *</label>
                    <input type="text" id="building" name="building" class="form-control"
                           value="<?php echo htmlspecialchars($venue['building']); ?>" required>
                </div>
            </div>
        </div>

        <div class="form-group">
            <label for="capacity">Capacity *</label>
            <input type="number" id="capacity" name="capacity" class="form-control"
                   value="<?php echo $venue['capacity']; ?>" min="1" required>
        </div>

        <div class="form-group">
            <label for="venuedescription">Description</label>
            <textarea id="venuedescription" name="venuedescription" class="form-control"><?php echo htmlspecialchars($venue['venuedescription']); ?></textarea>
        </div>

        <div class="form-group">
            <label for="features">Features</label>
            <textarea id="features" name="features" class="form-control"
                      placeholder="e.g., Projector, Whiteboard, Audio System"><?php echo htmlspecialchars($venue['features']); ?></textarea>
        </div>

        <div style="margin-top: 30px;">
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-save"></i> Update Venue
            </button>
            <a href="venues.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Venues
            </a>
        </div>
    </form>
</div>

</body>
</html>
